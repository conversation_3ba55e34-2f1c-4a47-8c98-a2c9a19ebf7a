//@version=6
indicator("Accurate Trading Signals Pro - vickymosafan", shorttitle="ATS Pro", overlay=true)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📊 INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// EMA Settings for Trend Analysis
ema_fast = input.int(9, title="Fast EMA", minval=1, group="📈 Trend Analysis")
ema_medium = input.int(21, title="Medium EMA", minval=1, group="📈 Trend Analysis")
ema_slow = input.int(50, title="Slow EMA", minval=1, group="📈 Trend Analysis")

// RSI Settings for Momentum
rsi_length = input.int(14, title="RSI Length", minval=1, group="📊 Momentum")
rsi_overbought = input.int(70, title="RSI Overbought", minval=50, maxval=100, group="📊 Momentum")
rsi_oversold = input.int(30, title="RSI Oversold", minval=0, maxval=50, group="📊 Momentum")

// MACD Settings for Confirmation
macd_fast = input.int(12, title="MACD Fast", minval=1, group="🔄 MACD")
macd_slow = input.int(26, title="MACD Slow", minval=1, group="🔄 MACD")
macd_signal = input.int(9, title="MACD Signal", minval=1, group="🔄 MACD")

// Signal Filtering
use_trend_filter = input.bool(true, title="Use Trend Filter", group="🎯 Signal Filtering")
min_signal_gap = input.int(10, title="Minimum Bars Between Signals", minval=1, group="🎯 Signal Filtering")

// Visual Settings
show_emas = input.bool(true, title="Show EMAs", group="🎨 Visual")
show_signals = input.bool(true, title="Show Buy/Sell Signals", group="🎨 Visual")
show_background = input.bool(true, title="Show Trend Background", group="🎨 Visual")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📈 TECHNICAL INDICATORS CALCULATION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// EMAs for Trend Analysis
ema_9 = ta.ema(close, ema_fast)
ema_21 = ta.ema(close, ema_medium)
ema_50 = ta.ema(close, ema_slow)

// RSI for Momentum
rsi = ta.rsi(close, rsi_length)

// MACD for Confirmation
[macd_line, signal_line, _] = ta.macd(close, macd_fast, macd_slow, macd_signal)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🎯 TREND ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Multi-EMA Trend Detection
bullish_trend = ema_9 > ema_21 and ema_21 > ema_50 and close > ema_9
bearish_trend = ema_9 < ema_21 and ema_21 < ema_50 and close < ema_9
neutral_trend = not bullish_trend and not bearish_trend

// Trend Strength
trend_strength = math.abs(ema_9 - ema_50) / ema_50 * 100

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🔍 SIGNAL GENERATION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Basic Conditions
ema_bullish_cross = ta.crossover(ema_9, ema_21)
ema_bearish_cross = ta.crossunder(ema_9, ema_21)

// RSI Conditions
rsi_bullish = rsi < rsi_overbought and rsi > 40  // Not overbought, has room to rise
rsi_bearish = rsi > rsi_oversold and rsi < 60   // Not oversold, has room to fall

// MACD Conditions
macd_bullish = macd_line > signal_line and macd_line > macd_line[1]
macd_bearish = macd_line < signal_line and macd_line < macd_line[1]

// Price Action Confirmation
price_above_ema21 = close > ema_21
price_below_ema21 = close < ema_21

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🎯 FILTERED SIGNAL CONDITIONS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// BUY Signal Conditions
buy_condition = ema_bullish_cross and rsi_bullish and macd_bullish and price_above_ema21 and (not use_trend_filter or bullish_trend or neutral_trend)

// SELL Signal Conditions
sell_condition = ema_bearish_cross and rsi_bearish and macd_bearish and price_below_ema21 and (not use_trend_filter or bearish_trend or neutral_trend)

// Signal Gap Filter
var int last_buy_bar = na
var int last_sell_bar = na

buy_signal = buy_condition and (na(last_buy_bar) or bar_index - last_buy_bar >= min_signal_gap)
sell_signal = sell_condition and (na(last_sell_bar) or bar_index - last_sell_bar >= min_signal_gap)

// Update last signal bars
if buy_signal
    last_buy_bar := bar_index
if sell_signal
    last_sell_bar := bar_index

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🎨 VISUAL ELEMENTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// EMA Plots
plot(show_emas ? ema_9 : na, title="EMA 9", color=color.new(color.blue, 0), linewidth=2)
plot(show_emas ? ema_21 : na, title="EMA 21", color=color.new(color.orange, 0), linewidth=2)
plot(show_emas ? ema_50 : na, title="EMA 50", color=color.new(color.red, 0), linewidth=2)

// Trend Background
bg_color = show_background ? (bullish_trend ? color.new(color.green, 95) : bearish_trend ? color.new(color.red, 95) : color.new(color.gray, 98)) : na
bgcolor(bg_color, title="Trend Background")

// Buy/Sell Signals
plotshape(show_signals and buy_signal, title="BUY Signal", location=location.belowbar, style=shape.labelup, size=size.normal, color=color.new(color.green, 0), textcolor=color.white, text="BUY")

plotshape(show_signals and sell_signal, title="SELL Signal", location=location.abovebar, style=shape.labeldown, size=size.normal, color=color.new(color.red, 0), textcolor=color.white, text="SELL")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📊 DASHBOARD (Table Display)
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Create Dashboard Table
var table dashboard = table.new(position.top_right, 2, 6, bgcolor=color.new(color.white, 80), border_width=1)

if barstate.islast
    // Header
    table.cell(dashboard, 0, 0, "Indicator", text_color=color.black, text_size=size.small, bgcolor=color.new(color.gray, 70))
    table.cell(dashboard, 1, 0, "Status", text_color=color.black, text_size=size.small, bgcolor=color.new(color.gray, 70))

    // Trend Status
    trend_text = bullish_trend ? "BULLISH" : bearish_trend ? "BEARISH" : "NEUTRAL"
    trend_color = bullish_trend ? color.green : bearish_trend ? color.red : color.gray
    table.cell(dashboard, 0, 1, "Trend", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 1, trend_text, text_color=color.white, text_size=size.small, bgcolor=color.new(trend_color, 20))

    // RSI Status
    rsi_text = rsi > rsi_overbought ? "OVERBOUGHT" : rsi < rsi_oversold ? "OVERSOLD" : "NEUTRAL"
    rsi_color = rsi > rsi_overbought ? color.red : rsi < rsi_oversold ? color.green : color.gray
    table.cell(dashboard, 0, 2, "RSI (" + str.tostring(math.round(rsi, 1)) + ")", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 2, rsi_text, text_color=color.white, text_size=size.small, bgcolor=color.new(rsi_color, 20))

    // MACD Status
    macd_text = macd_line > signal_line ? "BULLISH" : "BEARISH"
    macd_color = macd_line > signal_line ? color.green : color.red
    table.cell(dashboard, 0, 3, "MACD", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 3, macd_text, text_color=color.white, text_size=size.small, bgcolor=color.new(macd_color, 20))

    // Current Signal
    current_signal = buy_signal ? "BUY" : sell_signal ? "SELL" : "WAIT"
    signal_color = buy_signal ? color.green : sell_signal ? color.red : color.gray
    table.cell(dashboard, 0, 4, "Signal", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 4, current_signal, text_color=color.white, text_size=size.small, bgcolor=color.new(signal_color, 20))

    // Trend Strength
    strength_text = trend_strength > 2 ? "STRONG" : trend_strength > 1 ? "MODERATE" : "WEAK"
    table.cell(dashboard, 0, 5, "Strength", text_color=color.black, text_size=size.small)
    table.cell(dashboard, 1, 5, strength_text, text_color=color.black, text_size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🚨 ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Alert Conditions
alertcondition(buy_signal, title="BUY Signal Alert", message="🟢 BUY Signal Generated! Trend: {{plot_0}}, RSI: {{plot_1}}")
alertcondition(sell_signal, title="SELL Signal Alert", message="🔴 SELL Signal Generated! Trend: {{plot_0}}, RSI: {{plot_1}}")
alertcondition(buy_signal or sell_signal, title="Any Signal Alert", message="⚡ Trading Signal: {{plot_0}}")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📝 NOTES & STRATEGY EXPLANATION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// This indicator combines multiple technical analysis methods for accurate signal generation:
//
// 1. TREND ANALYSIS: Uses 3 EMAs (9, 21, 50) to determine market trend direction
//    - Bullish: EMA9 > EMA21 > EMA50 and price > EMA9
//    - Bearish: EMA9 < EMA21 < EMA50 and price < EMA9
//
// 2. MOMENTUM: RSI (14) to avoid overbought/oversold extremes
//    - Buy signals avoid RSI > 70 (overbought)
//    - Sell signals avoid RSI < 30 (oversold)
//
// 3. CONFIRMATION: MACD (12,26,9) for additional momentum confirmation
//    - Buy: MACD line > Signal line and rising
//    - Sell: MACD line < Signal line and falling
//
// 4. FILTERING: Multiple filters to reduce false signals
//    - Trend filter: Only trade in direction of main trend
//    - Signal gap: Minimum bars between signals to avoid noise
//    - Price confirmation: Price must be on correct side of EMA21
//
// 5. ACCURACY FOCUS: Designed to show SELL signals in bearish markets (like current XAUUSD)
//    and BUY signals in bullish markets, ensuring alignment with market direction