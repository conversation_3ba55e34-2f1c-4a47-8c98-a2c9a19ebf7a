// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © vickymosafan - Combined S&R and Trading Signals Indicator

//@version=6
indicator("Combined S&R + Trading Signals Pro by vickymosafan", shorttitle="Combined Pro", overlay=true, max_labels_count=500, max_lines_count=500, max_boxes_count=500, dynamic_requests=true)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📊 SUPPORT & RESISTANCE CONFIGURATION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// S&R Constants
const bool DEBUG = false
const bool fixSRs = true
const bool fixRetests = false
const int maxSRInfoListSize = 10
const int maxBarInfoListSize = 3000
const int maxDistanceToLastBar = 500
const int minSRSize = 5
const int retestLabelCooldown = 3
const float tooCloseATR = 1.0 / 8.0
const int labelOffsetBars = 20
const int atrLen = 20

// S&R Input Parameters
sr_pivotLength = input.int(15, "S&R Pivot Length", minval=3, maxval=50, group="📈 Support & Resistance", display=display.none)
sr_strength = input.int(1, "S&R Strength", [1, 2, 3], group="📈 Support & Resistance", display=display.none)
sr_invalidation = input.string("Close", "S&R Invalidation", ["Wick", "Close"], group="📈 Support & Resistance", display=display.none)
sr_expandZones = input.string("Only Valid", "Expand S&R Lines & Zones", options=["All", "Only Valid", "None"], group="📈 Support & Resistance", display=display.none)
sr_showInvalidated = input.bool(true, "Show Invalidated S&R", group="📈 Support & Resistance", display=display.none)

// S&R Timeframes
sr_timeframe1Enabled = input.bool(true, title="", group="📈 Support & Resistance", inline="timeframe1", display=display.none)
sr_timeframe1 = input.timeframe("", title="", group="📈 Support & Resistance", inline="timeframe1", display=display.none)
sr_timeframe2Enabled = input.bool(false, title="", group="📈 Support & Resistance", inline="timeframe2", display=display.none)
sr_timeframe2 = input.timeframe("D", title="", group="📈 Support & Resistance", inline="timeframe2", display=display.none)
sr_timeframe3Enabled = input.bool(false, title="", group="📈 Support & Resistance", inline="timeframe3", display=display.none)
sr_timeframe3 = input.timeframe("W", title="", group="📈 Support & Resistance", inline="timeframe3", display=display.none)

// S&R Breaks & Retests
sr_showBreaks = input.bool(true, "Show S&R Breaks", group="📈 Support & Resistance", inline="ShowBR", display=display.none)
sr_showRetests = input.bool(true, "Show S&R Retests", group="📈 Support & Resistance", inline="ShowBR", display=display.none)
sr_avoidFalseBreaks = input.bool(false, "Avoid False S&R Breaks", group="📈 Support & Resistance", display=display.none)
sr_breakVolumeThreshold = input.float(0.3, "S&R Break Volume Threshold", minval=0.1, maxval=2.0, step=0.1, group="📈 Support & Resistance", display=display.none)
sr_inverseBrokenLineColor = input.bool(false, "Inverse S&R Color After Broken", group="📈 Support & Resistance", display=display.none)

// S&R Style
sr_styleMode = input.string("Lines", "S&R Style", ["Lines", "Zones"], group="📈 Support & Resistance", display=display.none)
sr_lineStyle = input.string("____", "S&R Line Style", ["____", "----", "...."], group="📈 Support & Resistance", display=display.none)
sr_lineWidth = input.int(2, "S&R Line Width", minval=1, group="📈 Support & Resistance", display=display.none)
sr_zoneSize = input.float(1.0, "S&R Zone Width", minval=0.1, maxval=10, step=0.1, group="📈 Support & Resistance", display=display.none)
sr_supportColor = input.color(#08998180, "Support Color", group="📈 Support & Resistance", inline="RScolors", display=display.none)
sr_resistanceColor = input.color(#f2364580, "Resistance Color", group="📈 Support & Resistance", inline="RScolors", display=display.none)
sr_breakColor = input.color(color.blue, "S&R Break Color", group="📈 Support & Resistance", inline="RScolors2", display=display.none)
sr_textColor = input.color(#ffffff80, "S&R Text Color", group="📈 Support & Resistance", inline="RScolors2", display=display.none)

// S&R Alerts
sr_enableRetestAlerts = input.bool(true, "Enable S&R Retest Alerts", group="📈 Support & Resistance", display=display.none)
sr_enableBreakAlerts = input.bool(true, "Enable S&R Break Alerts", group="📈 Support & Resistance", display=display.none)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🎯 TRADING SIGNALS CONFIGURATION (ATS = Accurate Trading Signals)
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// ATS EMA Settings for Trend Analysis
ats_ema_fast = input.int(9, title="ATS Fast EMA", minval=1, group="🎯 Trading Signals")
ats_ema_medium = input.int(21, title="ATS Medium EMA", minval=1, group="🎯 Trading Signals")
ats_ema_slow = input.int(50, title="ATS Slow EMA", minval=1, group="🎯 Trading Signals")

// ATS RSI Settings for Momentum
ats_rsi_length = input.int(14, title="ATS RSI Length", minval=1, group="🎯 Trading Signals")
ats_rsi_overbought = input.int(70, title="ATS RSI Overbought", minval=50, maxval=100, group="🎯 Trading Signals")
ats_rsi_oversold = input.int(30, title="ATS RSI Oversold", minval=0, maxval=50, group="🎯 Trading Signals")

// ATS MACD Settings for Confirmation
ats_macd_fast = input.int(12, title="ATS MACD Fast", minval=1, group="🎯 Trading Signals")
ats_macd_slow = input.int(26, title="ATS MACD Slow", minval=1, group="🎯 Trading Signals")
ats_macd_signal = input.int(9, title="ATS MACD Signal", minval=1, group="🎯 Trading Signals")

// ATS Signal Filtering
ats_use_trend_filter = input.bool(true, title="ATS Use Trend Filter", group="🎯 Trading Signals")
ats_min_signal_gap = input.int(10, title="ATS Minimum Bars Between Signals", minval=1, group="🎯 Trading Signals")

// ATS Visual Settings
ats_show_emas = input.bool(true, title="Show ATS EMAs", group="🎯 Trading Signals")
ats_show_signals = input.bool(true, title="Show ATS Buy/Sell Signals", group="🎯 Trading Signals")
ats_show_background = input.bool(true, title="Show ATS Trend Background", group="🎯 Trading Signals")
ats_show_dashboard = input.bool(true, title="Show ATS Dashboard", group="🎯 Trading Signals")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📊 CALCULATIONS - SUPPORT & RESISTANCE
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// S&R Basic Calculations
atr = ta.atr(atrLen)
avgVolume = ta.sma(volume, atrLen)
var int curTFMS = timeframe.in_seconds(timeframe.period) * 1000
var map<string, bool> sr_alerts = map.new<string, bool>()
sr_alerts.put("Retest", false)
sr_alerts.put("Break", false)

sr_zoneSizeATR = sr_zoneSize * 0.075
sr_insideBounds = (bar_index > last_bar_index - maxDistanceToLastBar)

// S&R Data Types
type srInfo
    int startTime
    float price
    string srType
    int strength
    string timeframeStr
    bool ephemeral = false
    int breakTime
    array<int> retestTimes

type srObj
    srInfo info
    bool startFixed
    bool breakFixed
    bool rendered
    string combinedTimeframeStr
    line srLine
    box srBox
    label srLabel
    label breakLabel
    array<label> retestLabels

type barInfo
    int t
    int tc
    float c
    float h
    float l

// S&R Variables
var sr_allSRList = array.new<srObj>()
var sr_allSRInfoList = array.new<srInfo>()
var sr_barInfoList = array.new<barInfo>()

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🎯 CALCULATIONS - TRADING SIGNALS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// ATS EMAs for Trend Analysis
ats_ema_9 = ta.ema(close, ats_ema_fast)
ats_ema_21 = ta.ema(close, ats_ema_medium)
ats_ema_50 = ta.ema(close, ats_ema_slow)

// ATS RSI for Momentum
ats_rsi = ta.rsi(close, ats_rsi_length)

// ATS MACD for Confirmation
[ats_macd_line, ats_signal_line, _] = ta.macd(close, ats_macd_fast, ats_macd_slow, ats_macd_signal)

// ATS Trend Analysis
ats_bullish_trend = ats_ema_9 > ats_ema_21 and ats_ema_21 > ats_ema_50 and close > ats_ema_9
ats_bearish_trend = ats_ema_9 < ats_ema_21 and ats_ema_21 < ats_ema_50 and close < ats_ema_9
ats_neutral_trend = not ats_bullish_trend and not ats_bearish_trend
ats_trend_strength = math.abs(ats_ema_9 - ats_ema_50) / ats_ema_50 * 100

// ATS Signal Generation
ats_ema_bullish_cross = ta.crossover(ats_ema_9, ats_ema_21)
ats_ema_bearish_cross = ta.crossunder(ats_ema_9, ats_ema_21)
ats_rsi_bullish = ats_rsi < ats_rsi_overbought and ats_rsi > 40
ats_rsi_bearish = ats_rsi > ats_rsi_oversold and ats_rsi < 60
ats_macd_bullish = ats_macd_line > ats_signal_line and ats_macd_line > ats_macd_line[1]
ats_macd_bearish = ats_macd_line < ats_signal_line and ats_macd_line < ats_macd_line[1]
ats_price_above_ema21 = close > ats_ema_21
ats_price_below_ema21 = close < ats_ema_21

// ATS Filtered Signal Conditions
ats_buy_condition = ats_ema_bullish_cross and ats_rsi_bullish and ats_macd_bullish and ats_price_above_ema21 and (not ats_use_trend_filter or ats_bullish_trend or ats_neutral_trend)
ats_sell_condition = ats_ema_bearish_cross and ats_rsi_bearish and ats_macd_bearish and ats_price_below_ema21 and (not ats_use_trend_filter or ats_bearish_trend or ats_neutral_trend)

// ATS Signal Gap Filter
var int ats_last_buy_bar = na
var int ats_last_sell_bar = na
ats_buy_signal = ats_buy_condition and (na(ats_last_buy_bar) or bar_index - ats_last_buy_bar >= ats_min_signal_gap)
ats_sell_signal = ats_sell_condition and (na(ats_last_sell_bar) or bar_index - ats_last_sell_bar >= ats_min_signal_gap)

// Update ATS last signal bars
if ats_buy_signal
    ats_last_buy_bar := bar_index
if ats_sell_signal
    ats_last_sell_bar := bar_index

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🔧 SUPPORT & RESISTANCE FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

//#region Find Val RTN Time
sr_findValRtnTime (barInfo[] biList, valToFind, toSearch, searchMode, minTime, maxTime, int defVal = na) =>
    int rtnTime = defVal
    float minDiff = na
    if biList.size() > 0
        for i = biList.size() - 1 to 0
            curBI = biList.get(i)
            if curBI.t >= minTime and curBI.t < maxTime
                toLook = (toSearch == "Low" ? curBI.l : toSearch == "High" ? curBI.h : curBI.c)
                if searchMode == "Nearest"
                    curDiff = math.abs(valToFind - toLook)
                    if na(minDiff)
                        rtnTime := curBI.t
                        minDiff := curDiff
                    else
                        if curDiff <= minDiff
                            minDiff := curDiff
                            rtnTime := curBI.t
                if searchMode == "Higher"
                    if toLook >= valToFind
                        rtnTime := curBI.t
                        break
                if searchMode == "Lower"
                    if toLook <= valToFind
                        rtnTime := curBI.t
                        break
    rtnTime
//#endregion

sr_formatTimeframeString (string formatTimeframe, bool short = false) =>
    timeframeF = (formatTimeframe == "" ? timeframe.period : formatTimeframe)
    if str.contains(timeframeF, "D") or str.contains(timeframeF, "W") or str.contains(timeframeF, "S") or str.contains(timeframeF, "M")
        timeframe.from_seconds(timeframe.in_seconds(timeframeF))
    else
        seconds = timeframe.in_seconds(timeframeF)
        if seconds >= 3600
            hourCount = int(seconds / 3600)
            if short
                str.tostring(hourCount) + "h"
            else
                str.tostring(hourCount) + " Hour" + (hourCount > 1 ? "s" : "")
        else
            if short
                timeframeF + "m"
            else
                timeframeF + " Min"

sr_renderSRObj (srObj sr) =>
    if na(sr.info.breakTime) or sr_showInvalidated
        sr.rendered := true
        endTime = nz(sr.info.breakTime, time + curTFMS * labelOffsetBars)
        extendType = extend.none
        if na(sr.info.breakTime)
            extendType := extend.right
        if sr_expandZones == "Only Valid" and na(sr.info.breakTime)
            extendType := extend.both
        else if sr_expandZones == "All"
            extendType := extend.both
            endTime := time + curTFMS * labelOffsetBars

        labelTitle = sr_formatTimeframeString(sr.info.timeframeStr)
        if not na(sr.combinedTimeframeStr)
            labelTitle := sr.combinedTimeframeStr

        labelTitle += " | " + str.tostring(sr.info.price, format.mintick) + ((sr.info.ephemeral and DEBUG) ? " [E]" : "")
        if sr_styleMode == "Lines"
            // Line
            sr.srLine := line.new(sr.info.startTime, sr.info.price, endTime, sr.info.price, xloc = xloc.bar_time, color = sr.info.srType == "Resistance" ? sr_resistanceColor : sr_supportColor, width = sr_lineWidth, style = sr_lineStyle == "----" ? line.style_dashed : sr_lineStyle == "...." ? line.style_dotted : line.style_solid, extend = extendType)
            // Label
            sr.srLabel := label.new(extendType == extend.none ? ((sr.info.startTime + endTime) / 2) : endTime, sr.info.price, xloc = xloc.bar_time, text = labelTitle, textcolor = sr_textColor, style = label.style_none)
        else
            // Zone
            sr.srBox := box.new(sr.info.startTime, sr.info.price + atr * sr_zoneSizeATR, endTime, sr.info.price - atr * sr_zoneSizeATR, xloc = xloc.bar_time, bgcolor = sr.info.srType == "Resistance" ? sr_resistanceColor : sr_supportColor, border_color = na, text = labelTitle, text_color = sr_textColor, extend = extendType, text_size = size.normal, text_halign = (extendType != extend.none) ? text.align_right : text.align_center)

        // Break Label
        if sr_showBreaks
            if not na(sr.info.breakTime)
                sr.breakLabel := label.new(sr.info.breakTime, sr.info.price, "B", yloc = sr.info.srType ==  "Resistance" ? yloc.belowbar : yloc.abovebar, style = sr.info.srType == "Resistance" ? label.style_label_up : label.style_label_down, color = sr_breakColor, textcolor = color.new(sr_textColor, 0), xloc = xloc.bar_time, size = size.small)
                if (time - curTFMS <= sr.info.breakTime) and (time + curTFMS >= sr.info.breakTime)
                    sr_alerts.put("Break", true)

        // Retest Labels
        if sr_showRetests
            if sr.info.retestTimes.size() > 0
                for i = sr.info.retestTimes.size() - 1 to 0
                    curRetestTime = sr.info.retestTimes.get(i)
                    cooldownOK = true
                    if sr.retestLabels.size() > 0
                        lastLabel = sr.retestLabels.get(0)
                        if math.abs(lastLabel.get_x() - curRetestTime) < curTFMS * retestLabelCooldown
                            cooldownOK := false

                    if cooldownOK and (curRetestTime >= sr.info.startTime) and (na(sr.info.breakTime) or curRetestTime < sr.info.breakTime)
                        if time - curTFMS <= curRetestTime and time >= curRetestTime
                            sr_alerts.put("Retest", true)
                        sr.retestLabels.unshift(label.new(curRetestTime, sr.info.price, "R" + (DEBUG ?  (" " + str.tostring(sr.info.price)) : ""), yloc = sr.info.srType ==  "Resistance" ? yloc.abovebar : yloc.belowbar, style = sr.info.srType == "Resistance" ? label.style_label_down : label.style_label_up, color = sr.info.srType == "Resistance" ? sr_resistanceColor : sr_supportColor, textcolor = color.new(sr_textColor, 0), xloc = xloc.bar_time, size = size.small))

sr_safeDeleteSRObj (srObj sr) =>
    if sr.rendered
        line.delete(sr.srLine)
        box.delete(sr.srBox)
        label.delete(sr.srLabel)
        label.delete(sr.breakLabel)
        if sr.retestLabels.size() > 0
            for i = 0 to sr.retestLabels.size() - 1
                curRetestLabel = sr.retestLabels.get(i)
                label.delete(curRetestLabel)
        sr.rendered := false

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📊 SUPPORT & RESISTANCE LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// S&R Pivot Detection
sr_pivotHigh = ta.pivothigh(sr_pivotLength, sr_pivotLength)
sr_pivotLow = ta.pivotlow(sr_pivotLength, sr_pivotLength)

// S&R Bar Info Management
sr_barInfoList.unshift(barInfo.new(time, time_close, close, high, low))
if sr_barInfoList.size() > maxBarInfoListSize
    sr_barInfoList.pop()

// S&R Detection Logic
if sr_insideBounds and barstate.isconfirmed
    // Find Supports
    if not na(sr_pivotLow)
        validSR = true
        if sr_allSRInfoList.size() > 0
            for i = 0 to sr_allSRInfoList.size() - 1
                curRSInfo = sr_allSRInfoList.get(i)
                if (math.abs(curRSInfo.price - sr_pivotLow) < atr * tooCloseATR) and na(curRSInfo.breakTime)
                    validSR := false
                    break

        if validSR
            newSRInfo = srInfo.new(sr_barInfoList.get(sr_pivotLength).t, sr_pivotLow, "Support", 1, timeframe.period)
            newSRInfo.retestTimes := array.new<int>()
            sr_allSRInfoList.unshift(newSRInfo)
            while sr_allSRInfoList.size() > maxSRInfoListSize
                sr_allSRInfoList.pop()

    // Find Resistances
    if not na(sr_pivotHigh)
        validSR = true
        if sr_allSRInfoList.size() > 0
            for i = 0 to sr_allSRInfoList.size() - 1
                curRSInfo = sr_allSRInfoList.get(i)
                if (math.abs(curRSInfo.price - sr_pivotHigh) < atr * tooCloseATR) and na(curRSInfo.breakTime)
                    validSR := false
                    break
        if validSR
            newSRInfo = srInfo.new(sr_barInfoList.get(sr_pivotLength).t, sr_pivotHigh, "Resistance", 1, timeframe.period)
            newSRInfo.retestTimes := array.new<int>()
            sr_allSRInfoList.unshift(newSRInfo)
            if sr_allSRInfoList.size() > maxSRInfoListSize
                sr_allSRInfoList.pop()

// S&R Break and Retest Detection
if sr_insideBounds and (sr_invalidation == "Wick" or barstate.isconfirmed)
    if sr_allSRInfoList.size() > 0
        for i = 0 to sr_allSRInfoList.size() - 1
            srInfo curSRInfo = sr_allSRInfoList.get(i)

            // Breaks
            invHigh = (sr_invalidation == "Close" ? close : high)
            invLow = (sr_invalidation == "Close" ? close : low)
            closeTime = time
            if na(curSRInfo.breakTime)
                if curSRInfo.srType == "Resistance" and invHigh > curSRInfo.price
                    if (not sr_avoidFalseBreaks) or (volume > avgVolume * sr_breakVolumeThreshold)
                        curSRInfo.breakTime := closeTime
                        if sr_inverseBrokenLineColor and (not curSRInfo.ephemeral) and curSRInfo.strength >= sr_strength
                            ephSR = srInfo.new(closeTime, curSRInfo.price, "Support", curSRInfo.strength, curSRInfo.timeframeStr, true)
                            ephSR.retestTimes := array.new<int>()
                            sr_allSRInfoList.unshift(ephSR)
                else if curSRInfo.srType == "Support" and invLow < curSRInfo.price
                    if (not sr_avoidFalseBreaks) or (volume > avgVolume * sr_breakVolumeThreshold)
                        curSRInfo.breakTime := closeTime
                        if sr_inverseBrokenLineColor and (not curSRInfo.ephemeral) and curSRInfo.strength >= sr_strength
                            ephSR = srInfo.new(closeTime, curSRInfo.price, "Resistance", curSRInfo.strength, curSRInfo.timeframeStr, true)
                            ephSR.retestTimes := array.new<int>()
                            sr_allSRInfoList.unshift(ephSR)

            // Strength & Retests
            if na(curSRInfo.breakTime) and time > curSRInfo.startTime and barstate.isconfirmed
                if curSRInfo.srType == "Resistance" and high >= curSRInfo.price and close <= curSRInfo.price
                    int lastRetestTime = 0
                    if curSRInfo.retestTimes.size() > 0
                        lastRetestTime := curSRInfo.retestTimes.get(0)

                    if lastRetestTime != time
                        if not curSRInfo.ephemeral
                            curSRInfo.strength += 1
                        curSRInfo.retestTimes.unshift(time)

                else if curSRInfo.srType == "Support" and low <= curSRInfo.price and close >= curSRInfo.price
                    int lastRetestTime = 0
                    if curSRInfo.retestTimes.size() > 0
                        lastRetestTime := curSRInfo.retestTimes.get(0)

                    if lastRetestTime != time
                        if not curSRInfo.ephemeral
                            curSRInfo.strength += 1
                        curSRInfo.retestTimes.unshift(time)

// S&R Rendering Logic (Simplified for combined indicator)
if (bar_index > last_bar_index - maxDistanceToLastBar * 8) and barstate.isconfirmed
    if sr_allSRInfoList.size() > 0
        for i = 0 to sr_allSRInfoList.size() - 1
            srInfo curSRInfo = sr_allSRInfoList.get(i)
            if (curSRInfo.strength >= sr_strength) and (na(curSRInfo.breakTime) or (curSRInfo.breakTime - curSRInfo.startTime) >= minSRSize * curTFMS)
                srObj newSRObj = srObj.new(curSRInfo)
                newSRObj.retestLabels := array.new<label>()
                sr_renderSRObj(newSRObj)
                sr_allSRList.unshift(newSRObj)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🎨 VISUAL ELEMENTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// ATS EMA Plots
plot(ats_show_emas ? ats_ema_9 : na, title="ATS EMA 9", color=color.new(color.blue, 0), linewidth=2)
plot(ats_show_emas ? ats_ema_21 : na, title="ATS EMA 21", color=color.new(color.orange, 0), linewidth=2)
plot(ats_show_emas ? ats_ema_50 : na, title="ATS EMA 50", color=color.new(color.red, 0), linewidth=2)

// ATS Trend Background
ats_bg_color = ats_show_background ? (ats_bullish_trend ? color.new(color.green, 95) : ats_bearish_trend ? color.new(color.red, 95) : color.new(color.gray, 98)) : na
bgcolor(ats_bg_color, title="ATS Trend Background")

// ATS Buy/Sell Signals
plotshape(ats_show_signals and ats_buy_signal, title="ATS BUY Signal", location=location.belowbar, style=shape.labelup, size=size.normal, color=color.new(color.green, 0), textcolor=color.white, text="BUY")
plotshape(ats_show_signals and ats_sell_signal, title="ATS SELL Signal", location=location.abovebar, style=shape.labeldown, size=size.normal, color=color.new(color.red, 0), textcolor=color.white, text="SELL")

// ATS Dashboard (Table Display)
var ats_dashboard = table.new(position.top_right, 2, 6, bgcolor=color.new(color.white, 80), border_width=1)

if ats_show_dashboard and barstate.islast
    // Header
    table.cell(ats_dashboard, 0, 0, "ATS Indicator", text_color=color.black, text_size=size.small, bgcolor=color.new(color.gray, 70))
    table.cell(ats_dashboard, 1, 0, "Status", text_color=color.black, text_size=size.small, bgcolor=color.new(color.gray, 70))

    // Trend Status
    ats_trend_text = ats_bullish_trend ? "BULLISH" : ats_bearish_trend ? "BEARISH" : "NEUTRAL"
    ats_trend_color = ats_bullish_trend ? color.green : ats_bearish_trend ? color.red : color.gray
    table.cell(ats_dashboard, 0, 1, "Trend", text_color=color.black, text_size=size.small)
    table.cell(ats_dashboard, 1, 1, ats_trend_text, text_color=color.white, text_size=size.small, bgcolor=color.new(ats_trend_color, 20))

    // RSI Status
    ats_rsi_text = ats_rsi > ats_rsi_overbought ? "OVERBOUGHT" : ats_rsi < ats_rsi_oversold ? "OVERSOLD" : "NEUTRAL"
    ats_rsi_color = ats_rsi > ats_rsi_overbought ? color.red : ats_rsi < ats_rsi_oversold ? color.green : color.gray
    table.cell(ats_dashboard, 0, 2, "RSI (" + str.tostring(math.round(ats_rsi, 1)) + ")", text_color=color.black, text_size=size.small)
    table.cell(ats_dashboard, 1, 2, ats_rsi_text, text_color=color.white, text_size=size.small, bgcolor=color.new(ats_rsi_color, 20))

    // MACD Status
    ats_macd_text = ats_macd_line > ats_signal_line ? "BULLISH" : "BEARISH"
    ats_macd_color = ats_macd_line > ats_signal_line ? color.green : color.red
    table.cell(ats_dashboard, 0, 3, "MACD", text_color=color.black, text_size=size.small)
    table.cell(ats_dashboard, 1, 3, ats_macd_text, text_color=color.white, text_size=size.small, bgcolor=color.new(ats_macd_color, 20))

    // Current Signal
    ats_current_signal = ats_buy_signal ? "BUY" : ats_sell_signal ? "SELL" : "WAIT"
    ats_signal_color = ats_buy_signal ? color.green : ats_sell_signal ? color.red : color.gray
    table.cell(ats_dashboard, 0, 4, "Signal", text_color=color.black, text_size=size.small)
    table.cell(ats_dashboard, 1, 4, ats_current_signal, text_color=color.white, text_size=size.small, bgcolor=color.new(ats_signal_color, 20))

    // Trend Strength
    ats_strength_text = ats_trend_strength > 2 ? "STRONG" : ats_trend_strength > 1 ? "MODERATE" : "WEAK"
    table.cell(ats_dashboard, 0, 5, "Strength", text_color=color.black, text_size=size.small)
    table.cell(ats_dashboard, 1, 5, ats_strength_text, text_color=color.black, text_size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 🚨 ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// S&R Alert Conditions
alertcondition(sr_alerts.get("Retest"), "S&R New Retest", "Support/Resistance Retest Detected")
alertcondition(sr_alerts.get("Break"), "S&R New Break", "Support/Resistance Break Detected")

// ATS Alert Conditions
alertcondition(ats_buy_signal, title="ATS BUY Signal Alert", message="🟢 ATS BUY Signal Generated! Trend: " + ats_trend_text + ", RSI: " + str.tostring(math.round(ats_rsi, 1)))
alertcondition(ats_sell_signal, title="ATS SELL Signal Alert", message="🔴 ATS SELL Signal Generated! Trend: " + ats_trend_text + ", RSI: " + str.tostring(math.round(ats_rsi, 1)))
alertcondition(ats_buy_signal or ats_sell_signal, title="ATS Any Signal Alert", message="⚡ ATS Trading Signal Generated")

// Combined Alerts
if sr_enableRetestAlerts and sr_alerts.get("Retest")
    alert("S&R: New Retests Occurred.")

if sr_enableBreakAlerts and sr_alerts.get("Break")
    alert("S&R: New Breaks Occurred.")

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// 📝 NOTES & STRATEGY EXPLANATION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// This combined indicator merges two powerful trading tools:
//
// 1. SUPPORT & RESISTANCE ANALYSIS:
//    - Multi-timeframe S&R level detection using pivot points
//    - Break and retest identification with volume confirmation
//    - Dynamic strength calculation based on retests
//    - Visual representation with lines/zones and labels
//
// 2. ACCURATE TRADING SIGNALS:
//    - Triple EMA trend analysis (9, 21, 50)
//    - RSI momentum filtering to avoid extremes
//    - MACD confirmation for signal validation
//    - Trend-aligned signal generation with customizable filters
//    - Real-time dashboard showing market conditions
//
// COMBINED BENEFITS:
//    - S&R levels provide context for entry/exit points
//    - Trading signals help time entries at key levels
//    - Multiple confirmation layers reduce false signals
//    - Comprehensive market analysis in one indicator
